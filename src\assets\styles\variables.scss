/**
 * SCSS变量定义
 * @description 项目中使用的所有SCSS变量
 * <AUTHOR>
 * @date 2024-12-12
 * @version 1.0.0
 */

// 颜色系统
$color-primary: #409eff;
$color-primary-light: #66b1ff;
$color-primary-dark: #337ecc;

$color-success: #67c23a;
$color-success-light: #85ce61;
$color-success-dark: #529b2e;

$color-warning: #e6a23c;
$color-warning-light: #ebb563;
$color-warning-dark: #b88230;

$color-danger: #f56c6c;
$color-danger-light: #f78989;
$color-danger-dark: #c45656;

$color-info: #909399;
$color-info-light: #a6a9ad;
$color-info-dark: #73767a;

// 中性色
$color-white: #ffffff;
$color-black: #000000;
$color-gray-50: #fafafa;
$color-gray-100: #f5f5f5;
$color-gray-200: #eeeeee;
$color-gray-300: #e0e0e0;
$color-gray-400: #bdbdbd;
$color-gray-500: #9e9e9e;
$color-gray-600: #757575;
$color-gray-700: #616161;
$color-gray-800: #424242;
$color-gray-900: #212121;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// 边框颜色
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// 背景颜色
$background-color-base: #f5f7fa;
$background-color-light: #fafafa;
$background-color-lighter: #ffffff;
$background-color-disabled: #f5f7fa;
$background-color-hover: #f5f7fa;

// 字体
$font-family-base:
  -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
  sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
$font-family-mono:
  SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;

// 行高
$line-height-base: 1.5;
$line-height-sm: 1.25;
$line-height-lg: 1.75;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-2xl: 48px;
$spacing-3xl: 64px;

// 间距映射表（用于生成工具类）
$spacing-map: (
  'xs': $spacing-xs,
  'sm': $spacing-sm,
  'md': $spacing-md,
  'lg': $spacing-lg,
  'xl': $spacing-xl,
  '2xl': $spacing-2xl,
  '3xl': $spacing-3xl
);

// 圆角
$border-radius-sm: 2px;
$border-radius-base: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$border-radius-2xl: 16px;
$border-radius-full: 50%;
$border-radius-round: 50%;

// 阴影
$box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$box-shadow-base:
  0 1px 3px 0 rgba(0, 0, 0, 0.1),
  0 1px 2px 0 rgba(0, 0, 0, 0.06);
$box-shadow-md:
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$box-shadow-lg:
  0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$box-shadow-xl:
  0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);

// 过渡动画
$transition-duration: 0.3s;
$transition-timing: ease-in-out;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-2xl: 1600px;

// 断点映射表
$breakpoints: (
  'mobile': $breakpoint-xs,
  'tablet': $breakpoint-md,
  'desktop': $breakpoint-lg,
  'wide': $breakpoint-xl
);

// 容器最大宽度
$container-max-width-sm: 540px;
$container-max-width-md: 720px;
$container-max-width-lg: 960px;
$container-max-width-xl: 1140px;
$container-max-width-2xl: 1320px;

// 表格
$table-border-color: $border-color-light;
$table-header-bg: $background-color-light;
$table-row-hover-bg: $background-color-hover;

// 表单
$form-input-border-color: $border-color-base;
$form-input-focus-border-color: $color-primary;
$form-input-disabled-bg: $background-color-disabled;

// 按钮
$button-border-radius: $border-radius-base;
$button-font-weight: 500;
$button-padding-y: $spacing-sm;
$button-padding-x: $spacing-md;

// 布局
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;

// 深色主题颜色
$dark-background-color-base: #111827;
$dark-background-color-light: #1f2937;
$dark-background-color-lighter: #374151;
$dark-background-color-disabled: #374151;
$dark-background-color-hover: #374151;

// 深色主题文本颜色
$dark-text-color-primary: #f9fafb;
$dark-text-color-regular: #d1d5db;
$dark-text-color-secondary: #9ca3af;
$dark-text-color-placeholder: #6b7280;

// 深色主题边框颜色
$dark-border-color-base: #374151;
$dark-border-color-light: #4b5563;
$dark-border-color-lighter: #6b7280;
$dark-border-color-extra-light: #9ca3af;
